🔐 TapVerse Firestore Security Rules (Store & User Purchases)
==============================================================

Copy and paste these rules into your Firebase Console > Firestore Database > Rules:

rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // PUBLIC STORE ITEMS (READ-ONLY)
    match /store/items/{itemId} {
      allow read: if true;          // Anyone can view item list
      allow write: if false;        // Prevent editing from client
    }

    // BOOST STORE ITEMS (READ-ONLY)
    match /store/boosts/{boostId} {
      allow read: if true;
      allow write: if false;
    }

    // USER DATA
    match /users/{userId} {
      allow read, write: if request.auth.uid == userId;

      // Token balance
      match /tokens {
        allow read: if request.auth.uid == userId;
        allow update: if false; // Updated via Cloud Function or server only
      }

      // Owned Items
      match /ownedItems/{itemId} {
        allow read: if request.auth.uid == userId;
        allow create: if request.auth.uid == userId
                      && !exists(/databases/$(database)/documents/users/$(userId)/ownedItems/$(itemId));
        allow delete: if false;
      }

      // Active Skins & Flair
      match /activeSkins {
        allow read, update: if request.auth.uid == userId;
        allow create: if request.auth.uid == userId;
      }

      match /activeFlair {
        allow read, update: if request.auth.uid == userId;
        allow create: if request.auth.uid == userId;
      }

      // Boosts (allow 1 active at a time)
      match /boosts/activeBoost {
        allow read: if request.auth.uid == userId;
        allow create: if request.auth.uid == userId
                      && !exists(/databases/$(database)/documents/users/$(userId)/boosts/activeBoost);
        allow update: if request.auth.uid == userId;
        allow delete: if request.auth.uid == userId;
      }

      // User stats and achievements
      match /stats/{document=**} {
        allow read, write: if request.auth.uid == userId;
      }

      // Daily rewards
      match /dailyRewards/{document=**} {
        allow read, write: if request.auth.uid == userId;
      }

      // Purchase history (optional logging)
      match /purchaseHistory/{purchaseId} {
        allow read: if request.auth.uid == userId;
        allow create: if request.auth.uid == userId;
        allow update, delete: if false; // Immutable purchase records
      }
    }

    // LEADERBOARDS
    match /leaderboards/{gameId} {
      allow read: if true;
      allow write: if request.auth.uid != null
                   && request.resource.data.keys().hasOnly(['uid', 'score', 'timestamp'])
                   && request.resource.data.score is int;
    }

    // Game sessions (if needed for analytics)
    match /gameSessions/{sessionId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
    }
  }
}

🔐 SECURITY FEATURES INCLUDED:
==============================

✅ Read-only access to store items
✅ Secure token deduction on purchase (server-side only)
✅ Preventing duplicate ownership
✅ Restricting boost activation rules
✅ Leaderboard and score updates with validation
✅ Immutable purchase history logging
✅ Protection against client-side token manipulation

🧠 IMPORTANT SECURITY NOTES:
============================

🔐 Token Operations: Token deduction and balance updates are restricted to server-side operations only.
   Implement Cloud Functions for secure purchase transactions.

🎁 Mystery Draw Logic: Prize assignment and mystery box mechanics must be handled server-side to prevent
   manipulation.

❌ Client Restrictions: Clients cannot directly update token balances, delete owned items, or modify
   purchase history records.

🕓 Purchase Logging: Consider implementing the purchaseHistory collection for transaction auditing
   and customer support.

IMPORTANT STEPS TO APPLY THESE RULES:
=====================================

1. Go to Firebase Console: https://console.firebase.google.com/
2. Select your project: tapverse-b5d48
3. Navigate to Firestore Database
4. Click on "Rules" tab
5. Replace the existing rules with the rules above
6. Click "Publish" to apply the changes

ALSO ENSURE ANONYMOUS AUTHENTICATION IS ENABLED:
===============================================

1. In Firebase Console, go to Authentication
2. Click on "Sign-in method" tab
3. Make sure "Anonymous" is enabled
4. If not enabled, click on "Anonymous" and toggle it on

TESTING THE RULES:
==================

After applying these rules, your app should be able to:
- Sign in anonymously
- Create user documents
- Read/write user data
- Submit validated scores to leaderboards
- Read store items (but not modify them)
- View owned items and active customizations
- Activate/deactivate boosts (one at a time)

If you still get permission errors after applying these rules, check:
1. The user is properly authenticated (not null)
2. The document paths match exactly
3. The Firebase project ID is correct in your app configuration
4. Token operations are handled server-side via Cloud Functions

RECOMMENDED CLOUD FUNCTIONS FOR SECURE OPERATIONS:
=================================================

1. purchaseItem(userId, itemId, itemType) - Handles token deduction and item ownership
2. activateBoost(userId, boostId) - Manages boost activation with validation
3. openMysteryBox(userId, boxType) - Secure random prize distribution
4. dailyRewardClaim(userId) - Manages daily reward distribution
5. updateTokenBalance(userId, amount, reason) - Secure token balance updates
